name: Fullstack CI Pipeline

on:
  push:
    branches: [development, main]
  pull_request:
    branches: [development, main]

jobs:
  backend:
    name: 🔧 Backend Lint & Test
    runs-on: ubuntu-latest
    steps:
      - name: 🧾 Checkout repository
        uses: actions/checkout@v4

      - name: 🐹 Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: 'backend/go.mod'
          cache: true # Enable Go modules caching

      - name: 📦 Install Go dependencies
        working-directory: ./backend
        run: go mod tidy

      - name: ✅ Run Go unit tests
        working-directory: ./backend
        run: go test ./...

      - name: 🧹 Lint Go code
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.62.0
          working-directory: ./backend

  frontend:
    name: 🔧 Frontend Lint, Test & Build
    runs-on: ubuntu-latest
    steps:
      - name: 🧾 Checkout repository
        uses: actions/checkout@v4

      - name: ⬢ Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm' # Enable npm caching
          cache-dependency-path: frontend/package-lock.json

      - name: 📦 Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🧼 Lint frontend code (ESLint)
        working-directory: ./frontend
        run: npm run lint

      - name: ✅ Run frontend tests (Jest)
        working-directory: ./frontend
        run: npm test -- --ci --passWithNoTests

      - name: 🏗️ Verify frontend build
        working-directory: ./frontend
        run: npm run build
        env:
          NODE_OPTIONS: --max-old-space-size=4096
          NEXT_PUBLIC_API_URL: 'http://localhost:8080'
