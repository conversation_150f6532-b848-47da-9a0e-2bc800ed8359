package service

// import (
// 	"github.com/tajjjjr/social-network/backend/internal/utils"
// )

// // Test data with actual image signatures
// var testImageData = map[utils.ImageFormat][]byte{
// 	utils.JPEG: {0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46},
// 	utils.PNG:  {0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D},
// 	utils.GIF:  {0x47, 0x49, 0x46, 0x38, 0x37, 0x61, 0x10, 0x00, 0x10, 0x00},             // GIF87a
// 	utils.WebP: {0x52, 0x49, 0x46, 0x46, 0x24, 0x08, 0x00, 0x00, 0x57, 0x45, 0x42, 0x50}, // RIFF + size + WEBP
// 	utils.BMP:  {0x42, 0x4D, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
// 	utils.TIFF: {0x49, 0x49, 0x2A, 0x00, 0x08, 0x00, 0x00, 0x00}, // Little endian TIFF
// }

// var gif89aData = []byte{0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x10, 0x00, 0x10, 0x00} // GIF89a
// var tiffBigEndianData = []byte{0x4D, 0x4D, 0x00, 0x2A, 0x00, 0x08, 0x00, 0x00}      // Big endian TIFF


